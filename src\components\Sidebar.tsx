"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Accordion,
  AccordionItem,
} from "@nextui-org/react";
import Link from "next/link";

interface SidebarProps {
  isOpen: boolean;
  onClose?: () => void;
}

interface MenuItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  badge?: string;
  children?: MenuItem[];
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const menuItems: MenuItem[] = [
    {
      name: "Dashboard",
      href: "/",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
      ),
    },
    {
      name: "Products",
      href: "/products",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      badge: "124",
      children: [
        { name: "All Products", href: "/products", icon: <></> },
        { name: "Categories", href: "/products/categories", icon: <></> },
        { name: "Inventory", href: "/products/inventory", icon: <></> },
        { name: "Add Product", href: "/products/add", icon: <></> },
      ],
    },
    {
      name: "Orders",
      href: "/orders",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
      badge: "12",
      children: [
        { name: "All Orders", href: "/orders", icon: <></> },
        { name: "Pending", href: "/orders/pending", icon: <></> },
        { name: "Processing", href: "/orders/processing", icon: <></> },
        { name: "Completed", href: "/orders/completed", icon: <></> },
      ],
    },
    {
      name: "Customers",
      href: "/customers",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
    },
    {
      name: "Analytics",
      href: "/analytics",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
  ];

  const settingsItems: MenuItem[] = [
    {
      name: "Settings",
      href: "/settings",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    },
    {
      name: "Help & Support",
      href: "/support",
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
  ];

  const renderMenuItem = (item: MenuItem, isChild = false) => {
    if (item.children) {
      return (
        <AccordionItem
          key={item.name}
          aria-label={item.name}
          title={
            <div className="flex items-center gap-3">
              {item.icon}
              <span className={`${isChild ? 'text-sm' : ''}`}>{item.name}</span>
              {item.badge && (
                <Chip size="sm" color="primary" variant="flat">
                  {item.badge}
                </Chip>
              )}
            </div>
          }
          className="px-0"
        >
          <div className="pl-8 space-y-1">
            {item.children.map((child) => (
              <Link key={child.name} href={child.href}>
                <Button
                  variant="light"
                  className="w-full justify-start text-sm text-default-600 hover:text-primary"
                  startContent={child.icon}
                >
                  {child.name}
                </Button>
              </Link>
            ))}
          </div>
        </AccordionItem>
      );
    }

    return (
      <Link key={item.name} href={item.href}>
        <Button
          variant="light"
          className={`w-full justify-start ${isChild ? 'text-sm pl-8' : ''} hover:bg-default-100`}
          startContent={item.icon}
          endContent={
            item.badge && (
              <Chip size="sm" color="primary" variant="flat">
                {item.badge}
              </Chip>
            )
          }
        >
          {item.name}
        </Button>
      </Link>
    );
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-16 left-0 z-50 h-[calc(100vh-4rem)] w-64 
          transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:static lg:z-auto
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
      >
        <Card className="h-full rounded-none border-r border-divider">
          <CardBody className="p-0 overflow-y-auto">
            <div className="p-4 space-y-2">
              {/* Main navigation */}
              <div className="space-y-1">
                <Accordion
                  variant="light"
                  className="px-0"
                  itemClasses={{
                    base: "px-0",
                    title: "text-sm font-medium",
                    trigger: "px-3 py-2 hover:bg-default-100 rounded-lg",
                    content: "px-0 pb-2",
                  }}
                >
                  {menuItems.map((item) => renderMenuItem(item))}
                </Accordion>
              </div>

              <Divider className="my-4" />

              {/* Settings section */}
              <div className="space-y-1">
                <p className="text-xs font-semibold text-default-400 uppercase tracking-wider px-3 mb-2">
                  Settings
                </p>
                {settingsItems.map((item) => renderMenuItem(item))}
              </div>
            </div>
          </CardBody>
        </Card>
      </aside>
    </>
  );
}
