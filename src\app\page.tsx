import {
  <PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Progress,
  Divider,
} from "@nextui-org/react";

export default function Home() {
  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Welcome to ZP Market
        </h1>
        <p className="text-default-600">
          Your modern marketplace dashboard with header and sidebar navigation powered by HeroUI (NextUI)
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-default-600">Total Products</p>
                <p className="text-2xl font-bold">124</p>
              </div>
              <div className="p-2 bg-primary/10 rounded-lg">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-default-600">Pending Orders</p>
                <p className="text-2xl font-bold">12</p>
              </div>
              <div className="p-2 bg-warning/10 rounded-lg">
                <svg className="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-default-600">Total Customers</p>
                <p className="text-2xl font-bold">1,234</p>
              </div>
              <div className="p-2 bg-success/10 rounded-lg">
                <svg className="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-default-600">Revenue</p>
                <p className="text-2xl font-bold">$45,678</p>
              </div>
              <div className="p-2 bg-secondary/10 rounded-lg">
                <svg className="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Navigation Demo Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Navigation Features</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex items-center gap-3">
              <Chip color="primary" variant="flat">Header</Chip>
              <span className="text-sm">Responsive navbar with mobile menu</span>
            </div>
            <div className="flex items-center gap-3">
              <Chip color="secondary" variant="flat">Sidebar</Chip>
              <span className="text-sm">Collapsible sidebar with nested navigation</span>
            </div>
            <div className="flex items-center gap-3">
              <Chip color="success" variant="flat">Mobile</Chip>
              <span className="text-sm">Touch-friendly mobile navigation</span>
            </div>
            <div className="flex items-center gap-3">
              <Chip color="warning" variant="flat">Responsive</Chip>
              <span className="text-sm">Adapts to different screen sizes</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Quick Actions</h3>
          </CardHeader>
          <CardBody className="space-y-3">
            <Button color="primary" className="w-full justify-start" startContent={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            }>
              Add New Product
            </Button>
            <Button color="secondary" variant="flat" className="w-full justify-start" startContent={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            }>
              View Orders
            </Button>
            <Button color="success" variant="flat" className="w-full justify-start" startContent={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }>
              Analytics Dashboard
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Progress Section */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Monthly Goals</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm">Sales Target</span>
              <span className="text-sm">75%</span>
            </div>
            <Progress value={75} color="primary" />
          </div>
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm">Customer Acquisition</span>
              <span className="text-sm">60%</span>
            </div>
            <Progress value={60} color="secondary" />
          </div>
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm">Product Launches</span>
              <span className="text-sm">90%</span>
            </div>
            <Progress value={90} color="success" />
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
